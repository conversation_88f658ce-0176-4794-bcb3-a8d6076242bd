// Client-side Firebase Configuration
// This file handles Firebase initialization and authentication on the client side

// Firebase configuration - these will be injected by the server
let firebaseConfig = null;

// Firebase app and services
let app = null;
let auth = null;
let db = null;

// Initialize Firebase with configuration from server
async function initializeFirebase(config) {
    try {
        // Import Firebase modules dynamically
        const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
        const { 
            getAuth, 
            signInWithEmailAndPassword, 
            createUserWithEmailAndPassword,
            signOut,
            onAuthStateChanged,
            updateProfile,
            sendEmailVerification,
            sendPasswordResetEmail
        } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
        const { 
            getFirestore, 
            doc, 
            setDoc, 
            getDoc, 
            updateDoc,
            collection,
            query,
            where,
            getDocs
        } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

        // Store configuration
        firebaseConfig = config;
        
        // Initialize Firebase
        app = initializeApp(firebaseConfig);
        auth = getAuth(app);
        db = getFirestore(app);
        
        console.log('Firebase initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing Firebase:', error);
        return false;
    }
}

// Firebase Authentication functions
const FirebaseAuth = {
    // Sign up with email and password
    async signUp(email, password, username) {
        if (!auth) {
            throw new Error('Firebase not initialized');
        }
        
        try {
            const { createUserWithEmailAndPassword, updateProfile, sendEmailVerification } = 
                await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            const { doc, setDoc } = 
                await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
            
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Update the user's display name
            await updateProfile(user, {
                displayName: username
            });
            
            // Send email verification
            await sendEmailVerification(user);
            
            // Create user document in Firestore
            await setDoc(doc(db, 'users', user.uid), {
                uid: user.uid,
                username: username,
                email: email,
                createdAt: new Date().toISOString(),
                bio: "Excited to share my story and connect with the Naroop community",
                followers: 0,
                following: 0,
                stories: 0,
                emailVerified: false
            });
            
            return {
                success: true,
                user: {
                    uid: user.uid,
                    username: username,
                    email: user.email,
                    emailVerified: user.emailVerified
                }
            };
        } catch (error) {
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign in with email and password
    async signIn(email, password) {
        if (!auth) {
            throw new Error('Firebase not initialized');
        }
        
        try {
            const { signInWithEmailAndPassword } = 
                await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            const { doc, getDoc } = 
                await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
            
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Get user data from Firestore
            const userDoc = await getDoc(doc(db, 'users', user.uid));
            const userData = userDoc.exists() ? userDoc.data() : null;
            
            return {
                success: true,
                user: {
                    uid: user.uid,
                    username: userData?.username || user.displayName || user.email.split('@')[0],
                    email: user.email,
                    emailVerified: user.emailVerified,
                    ...userData
                }
            };
        } catch (error) {
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign out
    async signOut() {
        if (!auth) {
            throw new Error('Firebase not initialized');
        }
        
        try {
            const { signOut } = 
                await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            
            await signOut(auth);
            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Reset password
    async resetPassword(email) {
        if (!auth) {
            throw new Error('Firebase not initialized');
        }
        
        try {
            const { sendPasswordResetEmail } = 
                await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            
            await sendPasswordResetEmail(auth, email);
            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Get current user
    getCurrentUser() {
        return auth ? auth.currentUser : null;
    },

    // Listen to auth state changes
    onAuthStateChanged(callback) {
        if (!auth) {
            throw new Error('Firebase not initialized');
        }
        
        import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js')
            .then(({ onAuthStateChanged }) => {
                return onAuthStateChanged(auth, callback);
            });
    }
};

// Helper function to convert Firebase error codes to user-friendly messages
function getFirebaseErrorMessage(errorCode) {
    switch (errorCode) {
        case 'auth/user-not-found':
            return 'No account found with this email address.';
        case 'auth/wrong-password':
            return 'Incorrect password. Please try again.';
        case 'auth/email-already-in-use':
            return 'An account with this email already exists.';
        case 'auth/weak-password':
            return 'Password should be at least 6 characters long.';
        case 'auth/invalid-email':
            return 'Please enter a valid email address.';
        case 'auth/too-many-requests':
            return 'Too many failed attempts. Please try again later.';
        case 'auth/network-request-failed':
            return 'Network error. Please check your connection.';
        case 'auth/invalid-credential':
            return 'Invalid email or password. Please try again.';
        default:
            return 'An error occurred. Please try again.';
    }
}

// Export for global use
window.FirebaseAuth = FirebaseAuth;
window.initializeFirebase = initializeFirebase;
