// Firebase Configuration
// This file contains the Firebase configuration and initialization

// Import Firebase modules
import { initializeApp } from 'firebase/app';
import { 
    getAuth, 
    signInWithEmailAndPassword, 
    createUserWithEmailAndPassword,
    signOut,
    onAuthStateChanged,
    updateProfile,
    sendEmailVerification,
    sendPasswordResetEmail
} from 'firebase/auth';
import { 
    getFirestore, 
    doc, 
    setDoc, 
    getDoc, 
    updateDoc,
    collection,
    query,
    where,
    getDocs
} from 'firebase/firestore';

// Firebase configuration object
// These values should be set in environment variables for security
const firebaseConfig = {
    apiKey: process.env.FIREBASE_API_KEY || "your-api-key-here",
    authDomain: process.env.FIREBASE_AUTH_DOMAIN || "your-project.firebaseapp.com",
    projectId: process.env.FIREBASE_PROJECT_ID || "your-project-id",
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET || "your-project.appspot.com",
    messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || "123456789",
    appId: process.env.FIREBASE_APP_ID || "your-app-id"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
const db = getFirestore(app);

// Authentication functions
export const firebaseAuth = {
    // Sign up with email and password
    async signUp(email, password, username) {
        try {
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Update the user's display name
            await updateProfile(user, {
                displayName: username
            });
            
            // Send email verification
            await sendEmailVerification(user);
            
            // Create user document in Firestore
            await setDoc(doc(db, 'users', user.uid), {
                uid: user.uid,
                username: username,
                email: email,
                createdAt: new Date().toISOString(),
                bio: "Excited to share my story and connect with the Naroop community",
                followers: 0,
                following: 0,
                stories: 0,
                emailVerified: false
            });
            
            return {
                success: true,
                user: {
                    uid: user.uid,
                    username: username,
                    email: user.email,
                    emailVerified: user.emailVerified
                }
            };
        } catch (error) {
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign in with email and password
    async signIn(email, password) {
        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            
            // Get user data from Firestore
            const userDoc = await getDoc(doc(db, 'users', user.uid));
            const userData = userDoc.exists() ? userDoc.data() : null;
            
            return {
                success: true,
                user: {
                    uid: user.uid,
                    username: userData?.username || user.displayName || user.email.split('@')[0],
                    email: user.email,
                    emailVerified: user.emailVerified,
                    ...userData
                }
            };
        } catch (error) {
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign out
    async signOut() {
        try {
            await signOut(auth);
            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Reset password
    async resetPassword(email) {
        try {
            await sendPasswordResetEmail(auth, email);
            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Get current user
    getCurrentUser() {
        return auth.currentUser;
    },

    // Listen to auth state changes
    onAuthStateChanged(callback) {
        return onAuthStateChanged(auth, callback);
    }
};

// Firestore functions
export const firebaseDB = {
    // Get user data
    async getUserData(uid) {
        try {
            const userDoc = await getDoc(doc(db, 'users', uid));
            return userDoc.exists() ? userDoc.data() : null;
        } catch (error) {
            console.error('Error getting user data:', error);
            return null;
        }
    },

    // Update user data
    async updateUserData(uid, data) {
        try {
            await updateDoc(doc(db, 'users', uid), data);
            return { success: true };
        } catch (error) {
            console.error('Error updating user data:', error);
            return { success: false, error: error.message };
        }
    },

    // Check if username exists
    async checkUsernameExists(username) {
        try {
            const q = query(collection(db, 'users'), where('username', '==', username));
            const querySnapshot = await getDocs(q);
            return !querySnapshot.empty;
        } catch (error) {
            console.error('Error checking username:', error);
            return false;
        }
    }
};

// Helper function to convert Firebase error codes to user-friendly messages
function getFirebaseErrorMessage(errorCode) {
    switch (errorCode) {
        case 'auth/user-not-found':
            return 'No account found with this email address.';
        case 'auth/wrong-password':
            return 'Incorrect password. Please try again.';
        case 'auth/email-already-in-use':
            return 'An account with this email already exists.';
        case 'auth/weak-password':
            return 'Password should be at least 6 characters long.';
        case 'auth/invalid-email':
            return 'Please enter a valid email address.';
        case 'auth/too-many-requests':
            return 'Too many failed attempts. Please try again later.';
        case 'auth/network-request-failed':
            return 'Network error. Please check your connection.';
        default:
            return 'An error occurred. Please try again.';
    }
}

// Export the Firebase app and services
export { app, auth, db };
