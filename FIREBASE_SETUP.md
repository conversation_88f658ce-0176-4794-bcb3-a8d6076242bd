# Firebase Setup Guide for Naroop

This guide will help you set up Firebase Authentication for the Naroop social media platform.

## Phase 1: Firebase Project Setup

### Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name: `naroop-social-platform` (or your preferred name)
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

### Step 2: Enable Authentication

1. In your Firebase project console, click on "Authentication" in the left sidebar
2. Click "Get started" if this is your first time
3. Go to the "Sign-in method" tab
4. Click on "Email/Password"
5. Enable "Email/Password" authentication
6. Optionally enable "Email link (passwordless sign-in)" if desired
7. Click "Save"

### Step 3: Create a Web App

1. In the Firebase project overview, click the web icon (`</>`) to add a web app
2. Enter app nickname: `naroop-web-app`
3. Check "Also set up Firebase Hosting" if you plan to use Firebase Hosting (optional)
4. Click "Register app"
5. Copy the Firebase configuration object - you'll need this for the next step

### Step 4: Configure Environment Variables

1. Copy the `.env.example` file to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Open the `.env` file and update the Firebase configuration with your project details:
   ```env
   # Firebase Configuration
   FIREBASE_API_KEY=your-actual-api-key
   FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
   FIREBASE_PROJECT_ID=your-actual-project-id
   FIREBASE_STORAGE_BUCKET=your-project.appspot.com
   FIREBASE_MESSAGING_SENDER_ID=your-actual-sender-id
   FIREBASE_APP_ID=your-actual-app-id
   ```

### Step 5: Set up Firestore Database (Optional but Recommended)

1. In the Firebase console, click on "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" for development (you can secure it later)
4. Select a location for your database
5. Click "Done"

### Step 6: Configure Security Rules (Important for Production)

For development, you can use test mode, but for production, update your Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Posts can be read by anyone, but only created/updated by authenticated users
    match /posts/{postId} {
      allow read: if true;
      allow create, update: if request.auth != null;
      allow delete: if request.auth != null && 
        (request.auth.uid == resource.data.authorId || 
         request.auth.uid in resource.data.moderators);
    }
  }
}
```

## Phase 2: Testing the Setup

### Step 1: Start the Server

```bash
npm start
```

### Step 2: Check Firebase Configuration

1. Open your browser and go to `http://localhost:3000/api/firebase-config`
2. You should see your Firebase configuration (without sensitive data)
3. If you see an error, check your environment variables

### Step 3: Test Authentication

1. Go to `http://localhost:3000`
2. Try to sign up with a new account
3. Check the Firebase console under "Authentication" > "Users" to see if the user was created

## Troubleshooting

### Common Issues

1. **"Firebase not configured" error**
   - Check that all Firebase environment variables are set in your `.env` file
   - Restart the server after updating environment variables

2. **"Project not found" error**
   - Verify your `FIREBASE_PROJECT_ID` is correct
   - Make sure the project exists in your Firebase console

3. **Authentication errors**
   - Ensure Email/Password authentication is enabled in Firebase console
   - Check that your domain is authorized (for production deployments)

4. **CORS errors**
   - Make sure your domain is added to the authorized domains in Firebase console
   - For local development, `localhost` should be automatically authorized

### Environment Variables Reference

| Variable | Description | Example |
|----------|-------------|---------|
| `FIREBASE_API_KEY` | Your Firebase API key | `AIzaSyC...` |
| `FIREBASE_AUTH_DOMAIN` | Your project's auth domain | `myproject.firebaseapp.com` |
| `FIREBASE_PROJECT_ID` | Your Firebase project ID | `myproject-12345` |
| `FIREBASE_STORAGE_BUCKET` | Your project's storage bucket | `myproject.appspot.com` |
| `FIREBASE_MESSAGING_SENDER_ID` | Messaging sender ID | `123456789` |
| `FIREBASE_APP_ID` | Your Firebase app ID | `1:123:web:abc123` |

## Next Steps

Once Firebase is configured and working:

1. **Phase 2**: Replace local authentication with Firebase authentication
2. **Phase 3**: Migrate existing users (if any) to Firebase
3. **Phase 4**: Test all authentication flows and error handling

## Security Considerations

- Never commit your `.env` file to version control
- Use Firebase security rules to protect your data
- Enable email verification for new users
- Consider implementing additional security measures like rate limiting
- Regularly review and update your Firebase security rules

## Support

If you encounter issues:
1. Check the Firebase console for error messages
2. Review the browser console for client-side errors
3. Check the server logs for backend errors
4. Refer to the [Firebase documentation](https://firebase.google.com/docs)
